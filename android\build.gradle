buildscript {
    ext.kotlin_version = '1.9.0'
    repositories {
        google()
        jcenter()
    }

    dependencies {
     //   classpath 'com.android.tools.build:gradle:3.5.0'
        classpath 'com.android.tools.build:gradle:8.4.2'
        classpath "org.jetbrains.kotlin:kotlin-gradle-plugin:$kotlin_version"
        classpath 'com.google.gms:google-services:4.3.3'

        classpath 'com.google.firebase:perf-plugin:1.4.1'
        classpath 'com.google.firebase:firebase-crashlytics-gradle:2.7.1'
    }
}

allprojects {
    repositories {
        google()
        jcenter()
    }
}

rootProject.buildDir = '../build'
subprojects {
    project.buildDir = "${rootProject.buildDir}/${project.name}"
}
subprojects {
    project.evaluationDependsOn(':app')
}

tasks.register("clean", Delete) {
    delete rootProject.buildDir
}
