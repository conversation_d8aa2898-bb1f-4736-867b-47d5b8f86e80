name: flutterquiz
description: A new Flutter application.

# The following line prevents the package from being accidentally published to
# pub.dev using `pub publish`. This is preferred for private packages.
publish_to: "none" # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
version: 1.0.0+6

environment:
  sdk: ">=3.1.3 <4.0.0"

dependencies:
  # apple_sign_in_safety: ^0.0.6
  assets_audio_player_plus: ^3.1.2
  cached_network_image: ^3.4.1
  cloud_firestore: ^4.9.1
  connectivity_plus: ^4.0.2
  cupertino_icons: ^1.0.3
  # facebook_audience_network: ^1.0.0-nullsafety.0
  firebase_auth: ^4.9.0
  firebase_core: ^2.15.1
  firebase_messaging: ^14.6.7
  flutter:
    sdk: flutter
  flutter_bloc: ^8.0.1
  # flutter_exif_rotation: ^0.5.2
  flutter_html: ^3.0.0-alpha.1
  flutter_local_notifications: ^15.1.1
  flutter_localizations:
    sdk: flutter
  # flutter_login_facebook: ^1.8.0
  flutter_svg: ^2.0.0
  google_fonts: ^4.0.4
  google_mobile_ads: ^5.2.0
  google_sign_in: ^6.1.5
  hive: ^2.0.4
  hive_flutter: ^1.0.0
  http: ^1.2.2
  image_picker: ^1.0.4
  # in_app_purchase: ^2.0.0
  internet_connection_checker: ^1.0.0+1
  device_info_plus: ^11.2.0
  loading_overlay: ^0.3.0
  # ios_insecure_screen_detector: ^0.0.2
  intl: ^0.19.0
  intl_phone_field: ^3.2.0
  dart_ipify: ^1.1.1
  just_audio: ^0.9.12
  # launch_review: ^3.0.1
  lottie: ^2.6.0
  octo_image: ^2.1.0
  #otp_autofill: ^1.1.0
  package_info_plus: ^8.3.1
  path_provider: ^2.0.2
  pin_code_fields: ^8.0.1
  scratcher: ^2.1.0
  screenshot: ^3.0.0
  share_plus: ^11.1.0
  shared_preferences: ^2.0.6
  sms_autofill: ^2.2.0
  url_launcher: ^6.0.12
  wakelock_plus: ^1.3.0
  webview_flutter: ^4.8.0
  # country_code_picker: ^2.0.2
  # flutter_windowmanager: ^0.2.0
  encrypt:
  flutter_tex: ^4.0.3+4
  flutter_countdown_timer: ^4.1.0
  rotated_corner_decoration: ^2.0.0
  # purchases_flutter: ^5.6.5
  flutter_spinkit: ^5.1.0
  loader_overlay: ^2.0.6
  firebase_performance: ^0.9.2+5
  firebase_crashlytics: ^3.3.5
  dio: ^5.3.2
  firebase_performance_dio: ^0.5.0
  pretty_dio_logger: ^1.2.0-beta-1
  firebase_core_platform_interface: ^4.8.0
  flutter_swiper_plus: ^2.0.4
  flutter_widget_from_html: ^0.16.0
  easy_image_viewer: ^1.2.0
  amplitude_flutter: ^3.13.0
dev_dependencies:
  change_app_package_name: ^1.0.0
  flutter_test:
    sdk: flutter

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec
# The following section is specific to Flutter.
flutter:
  uses-material-design: true

  assets:
    - assets/google_fonts/
    - assets/images/
    - assets/images/profile/

    - assets/images/emojis/
    - assets/images/coins/

    - assets/languages/
    - assets/animations/

    - assets/sounds/
    - assets/files/
  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware.
  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages
  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: Lato
      fonts:
        - asset: assets/google_fonts/Lato-Regular.ttf